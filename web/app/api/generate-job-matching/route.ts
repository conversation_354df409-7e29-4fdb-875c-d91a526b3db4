export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase-server';
import { generateAIJobMatching } from '@/utils/ai-generators/jobMatchingGenerator';
import { createApiHandler } from '@/utils/apiErrorHandler';
import { captureApiError } from '@/utils/errorMonitoring';
import { trackApiUsage } from '@/lib/mixpanel-server';

async function handleRequest(request: NextRequest) {
  // Start timing the request
  const startTime = Date.now();
  let userId: string | undefined;
  
  let formData = new FormData();
  try {
    formData = await request.formData();
    
    const jobDescription = formData.get('jobDescription') as string | null;
    const jobImage = formData.get('jobImage') as File | null;
    const unauthenticatedResumeFile = formData.get('unauthenticatedResumeFile') as File | null;
    const unauthenticatedResumeFileName = formData.get('unauthenticatedResumeFileName') as string | null;
    
    if (!jobDescription && !jobImage) {
      return NextResponse.json({ 
        error: 'Deskripsi pekerjaan atau gambar lowongan diperlukan' 
      }, { status: 400 });
    }
    
    let buffer: ArrayBuffer;
    let fileName: string;

    if (unauthenticatedResumeFile && unauthenticatedResumeFileName) {
      buffer = await unauthenticatedResumeFile.arrayBuffer();
      fileName = unauthenticatedResumeFileName.toLowerCase();
    } else {
      // Create Supabase client
      const supabase = await createClient();

      // Get current user from cookies
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        console.error('Error getting user:', userError);
        return NextResponse.json({
          error: 'Anda harus login untuk menggunakan fitur ini'
        }, { status: 401 });
      }
      userId = user.id;

      // Get resume data from Supabase storage
      const { data: profile, error: fetchError } = await supabase
        .from('profiles')
        .select('resume_file_name')
        .eq('id', userId)
        .single();

      if (fetchError || !profile) {
        return NextResponse.json({ 
          error: 'Resume tidak ditemukan. Harap unggah resume terlebih dahulu.' 
        }, { status: 404 });
      }

      // Get the resume file from storage
      const { data: resumeFile, error: storageError } = await supabase.storage
        .from('resumes')
        .download(profile.resume_file_name);

      if (storageError || !resumeFile) {
        captureApiError('generate-job-matching', storageError || 'Resume file access error', {
          userId,
          type: 'resume_storage_access',
          resumeFileName: profile.resume_file_name
        });
        return NextResponse.json({ 
          error: 'Tidak dapat mengakses file resume. Harap unggah ulang resume Anda.' 
        }, { status: 500 });
      }

      // Convert the file to buffer and determine correct mime type
      buffer = await resumeFile.arrayBuffer();
      fileName = profile.resume_file_name.toLowerCase();
    } else {
      return NextResponse.json({ 
        error: 'Resume tidak ditemukan. Harap unggah resume terlebih dahulu.' 
      }, { status: 404 });
    }
    
    // Get the correct mime type based on file extension
    let mimeType: string;
    if (fileName.endsWith('.pdf')) {
      mimeType = 'application/pdf';
    } else if (fileName.endsWith('.docx')) {
      mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    } else if (fileName.endsWith('.png')) {
      mimeType = 'image/png';
    } else if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg')) {
      mimeType = 'image/jpeg';
    } else {
      mimeType = 'text/plain';
    }
    
    // Prepare job image if it exists
    let jobImageData: { buffer: ArrayBuffer, mimeType: string } | undefined;
    if (jobImage) {
      const jobImageBuffer = await jobImage.arrayBuffer();
      jobImageData = { 
        buffer: jobImageBuffer, 
        mimeType: jobImage.type 
      };
    }

    // Generate job matching analysis using AI with the file
    const matchResult = await generateAIJobMatching(
      { buffer, mimeType },
      jobDescription || undefined,
      jobImageData
    );
    
    // Calculate request duration
    const duration = Date.now() - startTime;
    
    // Track successful API usage in Mixpanel
    trackApiUsage(
      'generate-job-matching', 
      'success',
      duration,
      {
        has_job_description: !!jobDescription,
        has_job_image: !!jobImage,
        is_authenticated: !!userId,
        resume_type: fileName.split('.').pop(),
        // Track metrics for job matching feature (based on memory about improvements)
        match_result_structure: !!matchResult,
        has_skill_analysis: !!matchResult?.skillsAnalysis,
        has_tips: !!matchResult?.tips
      },
      userId
    );
    
    return NextResponse.json({ 
      success: true,
      matchResult
    });
    
  } catch (error) {
    console.error('Error generating job matching:', error);
    
    // Calculate request duration even for errors
    const duration = Date.now() - startTime;
    
    // Track failed API usage in Mixpanel
    trackApiUsage(
      'generate-job-matching', 
      'error',
      duration,
      {
        error_message: error instanceof Error ? error.message : 'Unknown error',
        is_authenticated: !!userId
      },
      userId
    );
    
    // Capture error details with Rollbar
    captureApiError('generate-job-matching', error, {
      position: formData?.get('position'),
      request_url: request.url
    });
    
    return NextResponse.json({
      success: false,
      error: 'Terjadi kesalahan saat menganalisis kecocokan pekerjaan'
    }, { status: 500 });
  }
}

// Export the handler with error reporting wrapper
export const POST = createApiHandler('generate-job-matching', handleRequest);
